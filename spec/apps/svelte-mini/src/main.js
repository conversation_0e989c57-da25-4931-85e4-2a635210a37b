import * as Sentry from "@sentry/svelte";
import { mount } from "svelte";
import App from "./App.svelte"

Sentry.init({
  dsn: null,
  debug: true,
  integrations: [Sentry.browserTracingIntegration(), Sentry.replayIntegration()],
  tracesSampleRate: 1.0,
  replaysSessionSampleRate: 1.0,
  replaysOnErrorSampleRate: 1.0,
  tracePropagationTargets: ["localhost"],
});

const app = mount(App, {
  target: document.getElementById("app"),
})

export default app
